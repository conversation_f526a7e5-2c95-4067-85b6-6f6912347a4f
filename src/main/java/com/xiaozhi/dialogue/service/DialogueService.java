package com.xiaozhi.dialogue.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.communication.server.mqtt.MqttServerPublish;
import com.xiaozhi.dao.MediaMapper;
import com.xiaozhi.dao.TaskInstanceMapper;
import com.xiaozhi.dialogue.domain.Sentence;
import com.xiaozhi.dialogue.domain.StartParams;
import com.xiaozhi.dialogue.intent.IntentService;
import com.xiaozhi.dialogue.llm.ChatService;
import com.xiaozhi.dialogue.llm.SentenceProcessor;
import com.xiaozhi.dialogue.service.VadService.VadStatus;
import com.xiaozhi.dialogue.stt.factory.SttServiceFactory;
import com.xiaozhi.dialogue.tts.Text2SpeechParams;
import com.xiaozhi.dialogue.tts.factory.TtsServiceFactory;
import com.xiaozhi.dto.ChatParams;
import com.xiaozhi.entity.SysConfig;
import com.xiaozhi.event.ChatSessionCloseEvent;
import com.xiaozhi.event.SessionCloseRequestEvent;
import com.xiaozhi.utils.AudioUtils;
import com.xiaozhi.utils.CozeUtil;
import com.xiaozhi.utils.EmojiUtils;
import com.xiaozhi.utils.EmojiUtils.EmoSentence;
import com.xiaozhi.utils.JsonUtil;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;

import java.nio.file.Path;
import java.text.DecimalFormat;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 对话处理服务
 * 负责处理语音识别和对话生成的业务逻辑
 */
@Service
public class DialogueService implements ApplicationListener<ChatSessionCloseEvent> {
    private static final Logger logger = LoggerFactory.getLogger(DialogueService.class);
    private static final DecimalFormat df = new DecimalFormat("0.00");

    @Resource
    private ChatService chatService;

    @Resource
    private AudioService audioService;

    @Resource
    private TtsServiceFactory ttsFactory;

    @Resource
    private SttServiceFactory sttFactory;

    @Resource
    private MusicService musicService;

    @Resource
    private HuiBenService huiBenService;

    @Resource
    private VadService vadService;

    @Resource
    private IntentService intentService;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Resource
    private MqttServerPublish mqttServerPublish;

    @Resource
    private AudioPushService audioPushService;

    @Resource
    private MediaMapper mediaMapper;

    // 会话状态管理
    private final Map<String, Long> llmStartTimes = new ConcurrentHashMap<>();

    // 音频发送顺序控制
    private final Map<String, ConcurrentHashMap<Integer, Sentence>> pendingSentences = new ConcurrentHashMap<>();
    private final Map<String, AtomicInteger> nextPlaySeq = new ConcurrentHashMap<>();

    // 句子发送完成回调管理
    private final Map<String, CompletableFuture<Void>> sentenceCompletionFutures = new ConcurrentHashMap<>();

    private final static List<String> GREETINGS = List.of(
            "耶！我在～想聊点啥呀？",
            "耶！我在～我们说英语吧？",
            "耶！我在～今天过得怎么样？",
            "嗨～你来啦，想聊点啥呀？",
            "嗨～你来啦，我们说英语吧？",
            "嗨～你来啦，今天过得怎么样？",
            "嘿嘿，想聊点啥呀？",
            "嘿嘿，我们说英语吧？",
            "嘿嘿，今天过得怎么样？");

    @Override
    public void onApplicationEvent(ChatSessionCloseEvent event) {
        ChatSession chatSession = event.getSession();
        if (chatSession != null) {
            // 清理会话对话数据（音频文件列表和助手回复文本会随着ChatSession对象一起被GC回收）
            cleanupSession(chatSession.getSessionId());
        }
    }

    /**
     * 处理音频数据
     */
    public void processAudioData(ChatSession session, byte[] opusData) {
        Thread.startVirtualThread(() -> {
            var vadResult = vadService.processAudio(session.getSessionId(), opusData);
            if (vadResult == null || vadResult.getStatus() == VadStatus.ERROR || vadResult.getData() == null) {
                return;
            }

            // 根据VAD状态处理
            switch (vadResult.getStatus()) {
                case SPEECH_START -> startStt(session, session.getCurrentRole().getSttConfig(), vadResult.getData());
                case SPEECH_CONTINUE -> {
                    // 语音继续，发送数据到流式识别
                    if (session.isStreamingState()) {
                        session.getAudioSinks().tryEmitNext(vadResult.getData());
                    }
                }
                case SPEECH_END -> {
                    // 语音结束，完成流式识别
                    if (session.isStreamingState()) {
                        session.getAudioSinks().tryEmitComplete();
                        session.setStreamingState(false);
                    }
                }
            }
        });
    }

    /**
     * 启动语音识别
     */
    private void startStt(ChatSession session, SysConfig sttConfig, byte[] initialAudio) {
        Assert.notNull(session, "session不能为空");

        Thread.startVirtualThread(() -> {
            // 创建新的音频数据接收管道
            session.createAudioStream();
            session.setStreamingState(true);

            var sttService = sttFactory.getSttService(sttConfig);
            if (sttService == null) {
                logger.error("无法获取STT服务 - Provider: {}", sttConfig != null ? sttConfig.getProvider() : "null");
                return;
            }

            try {
                // 接收初始音频数据
                if (initialAudio != null && initialAudio.length > 0) {
                    session.getAudioSinks().tryEmitNext(initialAudio);
                }

                // 设置用户收到音频的时间戳作为用户消息的创建时间戳，也用于约定保存音频文件的路径。一定要在STT前获得时间戳。
                final var userTimeMillis = System.currentTimeMillis();
                session.setUserTimeMillis(userTimeMillis);

                if (session.getAudioSinks() == null)
                    return;

                var sttNano = System.nanoTime();
                final var finalText = sttService.streamRecognition(session.getAudioSinks());
                if (!StringUtils.hasText(finalText)) {
                    return;
                }
                logger.info("语音识别结果: {}, 耗时 {} ms", finalText,
                        TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - sttNano));

                CompletableFuture.runAsync(() -> session.sendSttMessage(finalText))
                        .thenRun(() -> handleInputText(session, finalText, true))
                        .thenRun(() -> saveUserAudio(session, finalText.replaceAll("\\s+", "_")))
                        .exceptionally(e -> {
                            logger.error("处理对话失败: {}", e.getMessage(), e);
                            return null;
                        });
            } catch (Exception e) {
                logger.error("流式识别错误: {}", e.getMessage(), e);
            }
        });
    }

    public void sendSentence(ChatSession session, String text, boolean isAdditional) {
        if (session.isAudioPlaying()) {
            logger.warn("设备正在播放音频，取消主动推送消息 {}", text);
            return;
        }

        final int chunkSize = 10;

        if (isAdditional) {
            session.getAdditionalMessages().add(text);
        }

        initChat(session);

        CompletableFuture.runAsync(session::sendTTSStart)
                .thenRun(() -> {
                    // 设置LLM生成消息的时间戳作为Assistant消息的创建时间戳，也用于约定保存音频文件的路径。一定要在LLM前设置时间戳。
                    session.setAssistantTimeMillis(System.currentTimeMillis());
                    // 重置当前对话的音频和文本数据
                    session.resetDialogueData();

                    final SentenceProcessor.StatefulSentenceProcessor processor = new SentenceProcessor.StatefulSentenceProcessor();
                    final AtomicInteger sentenceCount = new AtomicInteger(0);

                    Flux.<String>create(sink -> {
                                for (var i = 0; i < text.length(); i += chunkSize) {
                                    sink.next(text.substring(i, Math.min(text.length(), i + chunkSize)));
                                }
                                sink.complete();
                            })
                            .flatMap(token -> {
                                String sentence = processor.processToken(token);
                                if (sentence != null && !sentence.isEmpty()) {
                                    return Flux.just(sentence);
                                }
                                return Flux.empty();
                            })
                            .map(sentence -> {
                                int seq = sentenceCount.incrementAndGet();
                                return new Sentence(seq, sentence, seq == 1, false);
                            })
                            .concatWith(Flux.defer(() -> {
                                // 获取最后的句子并标记为最后一句
                                String lastSentence = processor.getLastSentence();
                                int finalSeq = sentenceCount.incrementAndGet();

                                if (lastSentence != null && !lastSentence.isEmpty()) {
                                    return Flux.just(new Sentence(finalSeq, lastSentence, finalSeq == 1, true));
                                }
                                // 如果没有剩余句子，发送一个空的结束标记
                                return Flux.just(new Sentence(finalSeq, "", finalSeq == 1, true));
                            }))
                            .subscribe(
                                    sentence -> {
                                        logger.debug("处理句子: seq={}, text='{}', isFirst={}, isLast={}",
                                                sentence.getSeq(), sentence.getText(), sentence.isFirst(),
                                                sentence.isLast());
                                        handleSentence(session, sentence);
                                    },
                                    error -> {
                                        logger.error("流式处理出错: {}", error.getMessage(), error);
                                        // 创建错误句子对象
                                        Sentence errorSentence = new Sentence(1, "抱歉，我在处理您的请求时遇到了问题。", true, true);
                                        handleSentence(session, errorSentence);
                                    },
                                    () -> logger.debug("句子处理完成"));
                })
                .exceptionally(e -> {
                    logger.error("处理对话失败: {}", e.getMessage(), e);
                    return null;
                });
    }

    public CompletableFuture<Void> sendOneSentence(ChatSession session, String text, boolean isAdditional) {
        if (isAdditional) {
            session.getAdditionalMessages().add(text);
        }

        initChat(session);

        session.setUserTimeMillis(System.currentTimeMillis());
        // 设置LLM生成消息的时间戳作为Assistant消息的创建时间戳，也用于约定保存音频文件的路径。一定要在LLM前设置时间戳。
        final Long assistantTimeMillis = System.currentTimeMillis();
        session.setAssistantTimeMillis(assistantTimeMillis);
        // 重置当前对话的音频和文本数据
        session.resetDialogueData();

        var sentence = new Sentence(1, text, true, true);

        // 如果是最后一句，保存对话消息到数据库
        if (sentence.isLast()) {
            Thread.startVirtualThread(() -> {
                // 使用 ChatSession 中的助手回复文本
                var assistantResponseText = session.getAssistantResponse();

                if (!assistantResponseText.isEmpty()) {
                    var assistantMessage = new AssistantMessage(assistantResponseText);

                    // 保存用户消息和助手消息到数据库
                    if (isAdditional) {
                        session.getConversation().addMessage(
                                null,
                                session.getUserTimeMillis(),
                                assistantMessage,
                                assistantTimeMillis);
                    }
                }

            });
        }

        return handleSentence(session, sentence);
    }

    private void saveUserAudio(ChatSession session, String filename) {
        try {
            // 获取当前语音活动的PCM数据
            List<byte[]> pcmFrames = vadService.getPcmData(session.getSessionId());

            if (pcmFrames != null && !pcmFrames.isEmpty()) {
                // 计算总大小并合并PCM帧
                int totalSize = pcmFrames.stream().mapToInt(frame -> frame.length).sum();
                byte[] fullPcmData = new byte[totalSize];
                int offset = 0;

                for (byte[] frame : pcmFrames) {
                    System.arraycopy(frame, 0, fullPcmData, offset, frame.length);
                    offset += frame.length;
                }

                // 保存为WAV文件
                var path = Path.of(AudioUtils.AUDIO_PATH, session.getSessionId(), STR."\{filename}.wav");
                AudioUtils.saveAsWav(path, fullPcmData);
            }
        } catch (Exception e) {
            logger.error("保存用户音频失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 保存用户音频数据
     */
    private void saveUserAudio(ChatSession session) {
        try {
            // 获取当前语音活动的PCM数据
            List<byte[]> pcmFrames = vadService.getPcmData(session.getSessionId());

            if (pcmFrames != null && !pcmFrames.isEmpty()) {
                // 计算总大小并合并PCM帧
                int totalSize = pcmFrames.stream().mapToInt(frame -> frame.length).sum();
                byte[] fullPcmData = new byte[totalSize];
                int offset = 0;

                for (byte[] frame : pcmFrames) {
                    System.arraycopy(frame, 0, fullPcmData, offset, frame.length);
                    offset += frame.length;
                }

                // 保存为WAV文件
                Path path = session.getUserAudioPath();
                AudioUtils.saveAsWav(path, fullPcmData);

                logger.debug("用户音频已保存: {}", path.toString());
            }
        } catch (Exception e) {
            logger.error("保存用户音频失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 初始化对话状态
     */
    private void initChat(ChatSession session) {
        String sessionId = session.getSessionId();
        llmStartTimes.put(sessionId, System.currentTimeMillis());

        // 重新初始化音频发送顺序控制（每次对话都要重置）
        ConcurrentHashMap<Integer, Sentence> pending = new ConcurrentHashMap<>();
        pendingSentences.put(sessionId, pending);
        nextPlaySeq.put(sessionId, new AtomicInteger(1));

        // 初始化 ChatSession 的播放状态
        session.setAudioPlaying(false);

        logger.debug("初始化对话状态 - sessionId: {}, nextPlaySeq: 1", sessionId);
    }

    /**
     * 清理音频发送状态
     */
    private void clearAudioSendingState(ChatSession session) {
        var pending = pendingSentences.get(session.getSessionId());
        if (pending != null) {
            pending.clear();
        }

        var nextSeq = nextPlaySeq.get(session.getSessionId());
        if (nextSeq != null) {
            nextSeq.set(1);
        }

        // 通过 SessionManager 清理播放状态
        session.setPlaying(false);

        logger.debug("清理音频发送状态 - sessionId: {}", session.getSessionId());
    }

    /**
     * 处理LLM返回的句子
     * 使用虚拟线程处理TTS生成
     */
    private CompletableFuture<Void> handleSentence(ChatSession session, Sentence sentence) {
        Assert.notNull(session, "session cannot be null");
        Assert.notNull(sentence, "sentence cannot be null");

        CompletableFuture<Void> future = new CompletableFuture<>();
        if (!session.isOpen() || session.isClientAbort()) {
            future.complete(null);
            return future;
        }

        Long assistantTimeMillis = session.getAssistantTimeMillis();
        Assert.notNull(assistantTimeMillis, "assistantTimeMillis cannot be null");
        String sessionId = session.getSessionId();
        String text = sentence.getText();

        // 累加完整回复内容到 ChatSession
        if (text != null && !text.isEmpty()) {
            session.appendAssistantResponse(text);
        }

        // 计算模型响应时间
        final double responseTime;
        Long startTime = llmStartTimes.get(sessionId);
        if (startTime != null) {
            responseTime = (System.currentTimeMillis() - startTime) / 1000.0;
        } else {
            responseTime = 0.0;
        }

        // 设置句子的额外属性
        sentence.setModelResponseTime(responseTime); // 记录模型响应时间
        sentence.setAssistantTimeMillis(assistantTimeMillis); // 设置对话ID

        // 处理表情符号
        var emoSentence = EmojiUtils.processSentence(text);

        // 如果句子为空，跳过TTS处理
        if (text == null || text.isEmpty() || emoSentence.getTtsSentence().isEmpty()) {
            sentence.setAudio(null);
            sentence.setTtsGenerationTime(0); // 设置TTS生成时间为0

            // 使用顺序控制发送音频消息（空音频）
            sendAudioInOrder(session, sentence);
            future.complete(null);
            return future;
        }

        // 使用虚拟线程异步生成音频文件，返回 Future
        return generateAudioAndSend(session, sentence, emoSentence,
                session.getCurrentRole().getTtsConfig(),
                session.getCurrentRole().getVoice(),
                session.getCurrentRole().getSpeed());
    }

    /**
     * 生成音频并直接发送
     *
     * @return CompletableFuture<Void> 音频发送完成后的Future
     */
    private CompletableFuture<Void> generateAudioAndSend(
            ChatSession session,
            Sentence sentence,
            EmoSentence emoSentence,
            SysConfig ttsConfig,
            String voiceName,
            double speed) {

        CompletableFuture<Void> future = new CompletableFuture<>();

        CompletableFuture.runAsync(() -> {
            if (!session.isOpen() || session.isClientAbort()) {
                logger.warn("会话已关闭, 停止TTS执行");
                future.complete(null);
                return;
            }

            try {
                long ttsStartTime = System.currentTimeMillis();
                logger.info("TTS sentence is {}", emoSentence.getTtsSentence());
                String audioPath = ttsFactory.getTtsService(ttsConfig, voiceName)
                        .textToSpeech(new Text2SpeechParams(emoSentence.getTtsSentence(), speed));
                long ttsDuration = System.currentTimeMillis() - ttsStartTime;

                // 记录TTS生成时间和心情
                sentence.setTtsGenerationTime(ttsDuration / 1000.0);
                sentence.setMoods(emoSentence.getMoods());
                sentence.setAudio(audioPath);

                // 记录日志
                logger.info("句子音频生成完成 - 序号: {}, 路径: {}, 对话ID: {}, 模型响应: {}秒, 语音生成: {}秒, 内容: \"{}\"",
                        sentence.getSeq(), audioPath, sentence.getAssistantTimeMillis(),
                        df.format(sentence.getModelResponseTime()),
                        df.format(sentence.getTtsGenerationTime()),
                        sentence.getText());

                // 将音频路径添加到 ChatSession 中
                if (audioPath != null) {
                    session.addAudioFile(audioPath);
                }

                // 使用顺序控制发送音频消息，并等待发送完成
                sendAudioInOrderWithCallback(session, sentence, future);

            } catch (Exception e) {
                e.printStackTrace();
                logger.error("TTS生成失败 - 序号: {}, 错误: {}", sentence.getSeq(), e.getMessage());

                // 即使失败也要发送消息（无音频）
                sentence.setAudio(null);
                sentence.setTtsGenerationTime(0);

                // 使用顺序控制发送音频消息，并等待发送完成
                sendAudioInOrderWithCallback(session, sentence, future);
            }
        }, Executors.newVirtualThreadPerTaskExecutor());

        return future;
    }

    /**
     * 按顺序发送音频消息
     */
    private void sendAudioInOrder(ChatSession session, Sentence sentence) {
        sendAudioInOrderWithCallback(session, sentence, null);
    }

    /**
     * 按顺序发送音频消息，并在发送完成后调用回调
     */
    private void sendAudioInOrderWithCallback(ChatSession session, Sentence sentence, CompletableFuture<Void> completionFuture) {
        if (!session.isOpen() || session.isClientAbort()) {
            if (completionFuture != null) {
                completionFuture.complete(null);
            }
            return;
        }

        String sessionId = session.getSessionId();
        int seq = sentence.getSeq();

        logger.debug("添加句子到发送队列 - sessionId: {}, seq: {}, text: '{}', isLast: {}",
                sessionId, seq, sentence.getText(), sentence.isLast());

        // 将句子添加到待发送队列
        ConcurrentHashMap<Integer, Sentence> pending = pendingSentences.get(sessionId);
        if (pending == null) {
            logger.warn("待发送队列为空 - sessionId: {}", sessionId);
            if (completionFuture != null) {
                completionFuture.complete(null);
            }
            return;
        }

        // 如果有完成回调，将其与句子关联
        if (completionFuture != null) {
            // 创建一个映射来存储句子的完成回调
            String key = sessionId + "_" + seq;
            sentenceCompletionFutures.put(key, completionFuture);
        }

        pending.put(seq, sentence);

        // 尝试发送下一个应该播放的句子
        tryPlayNext(session, sessionId);
    }

    /**
     * 尝试播放下一个句子
     */
    private synchronized void tryPlayNext(ChatSession session, String sessionId) {
        AtomicInteger nextSeq = nextPlaySeq.get(sessionId);
        ConcurrentHashMap<Integer, Sentence> pending = pendingSentences.get(sessionId);

        if (nextSeq == null || pending == null) {
            logger.warn("播放状态异常 - sessionId: {}, nextSeq: {}, playing: {}, pending: {}",
                    sessionId, nextSeq, session.isPlaying(), pending != null);
            return;
        }

        // 如果正在播放，等待当前播放完成
        if (session.isPlaying()) {
            logger.debug("当前正在播放，等待完成 - sessionId: {}", sessionId);
            return;
        }

        // 检查下一个序号的句子是否准备好
        int expectedSeq = nextSeq.get();
        Sentence nextSentence = pending.get(expectedSeq);

        logger.debug("尝试播放下一个句子 - sessionId: {}, expectedSeq: {}, 队列中的句子: {}",
                sessionId, expectedSeq, pending.keySet());

        if (nextSentence != null) {
            logger.info("开始播放句子 - sessionId: {}, seq: {}, text: '{}', isLast: {}",
                    sessionId, expectedSeq, nextSentence.getText(), nextSentence.isLast());

            // 从待发送队列中移除
            pending.remove(expectedSeq);

            // 更新下一个期望的序号
            nextSeq.incrementAndGet();

            // 发送音频消息
            audioService.sendAudioMessage(session, nextSentence)
                    .thenRun(() -> {
                        logger.debug("句子播放完成 - sessionId: {}, seq: {}", sessionId, expectedSeq);

                        // 触发句子发送完成回调
                        String completionKey = sessionId + "_" + expectedSeq;
                        CompletableFuture<Void> completionFuture = sentenceCompletionFutures.remove(completionKey);
                        if (completionFuture != null) {
                            completionFuture.complete(null);
                            logger.debug("句子发送完成回调已触发 - sessionId: {}, seq: {}", sessionId, expectedSeq);
                        }

                        // 如果是最后一句，保存响应
                        if (nextSentence.isLast() && nextSentence.getAssistantTimeMillis() != null) {
                            saveAssistantResponse(session);
                        }

                        // 尝试播放下一个句子
                        tryPlayNext(session, sessionId);
                    });
        } else {
            logger.debug("期望的句子尚未准备好 - sessionId: {}, expectedSeq: {}", sessionId, expectedSeq);
        }
    }

    /**
     * 保存助手的完整响应（文本和合并音频）
     */
    private void saveAssistantResponse(ChatSession session) {
        Long assistantTimeMillis = session.getAssistantTimeMillis();
        try {
            // 获取该对话的所有音频路径（已按顺序存储）
            List<String> audioFilesToMerge = session.getAudioFiles();
            if (audioFilesToMerge.isEmpty()) {
                logger.warn("对话 {} 没有可用的音频路径", assistantTimeMillis);
                return;
            }

            // 合并音频文件
            Path path = session.getAssistantAudioPath();
            AudioUtils.mergeAudioFiles(path, audioFilesToMerge);
            // 保存合并后的音频路径
            logger.info("对话 {} 的音频已合并: {}, 文件数: {}", assistantTimeMillis, path, audioFilesToMerge.size());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("保存助手响应失败 - 对话ID: {}, 错误: {}", assistantTimeMillis, e.getMessage(), e);
        }
    }

    /**
     * 处理语音唤醒
     */
    public void handleWakeWord(ChatSession session, String text) {
        logger.info("检测到唤醒词: \"{}\"", text);
        try {
            var device = session.getSysDevice();
            if (device == null) {
                return;
            }

            // var idx = ThreadLocalRandom.current().nextInt(GREETINGS.size());
            // sendSentenceWithoutSegmentation(session, GREETINGS.get(idx), false);
            sendOneSentence(session, "我在", false);
        } catch (Exception e) {
            logger.error("处理唤醒词失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理文本消息交互
     * 如果指定了输出文本，则用指定的文本生成语音
     */
    public void handleInputText(ChatSession session, String inputText, boolean useTool) {
        // 连接关闭或主动打断时
        if (!session.isOpen() || session.isClientAbort()) {
            return;
        }

        // 重置当前对话的音频和文本数据
        session.resetDialogueData();

        Thread.startVirtualThread(() -> {

            session.setLastActivityTime(Instant.now());

            // llm intent recognition
            if (useTool) {
                var intentStart = System.nanoTime();
                var intentResult = JsonUtil.fromJson(intentService.recognize(session, inputText),
                        new TypeReference<Map<String, Object>>() {
                        });
                logger.info("Intent time used {} ms",
                        TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - intentStart));

                var functionInfo = (Map<String, Object>) intentResult.get("function");
                var funcName = (String) functionInfo.get("name");
                logger.info("Intent function name is {}", funcName);
                var text = (String) intentResult.get("result");
                if (!funcName.equals("continue_chat") && StringUtils.hasText(text)) {
                    this.sendOneSentence(session, text, false)
                            .thenRun(() -> {
                                if (funcName.equals("func_stop_chat")) {
                                    // 等待音频播放完毕
                                    try {
                                        TimeUnit.SECONDS.sleep(2);
                                    } catch (InterruptedException e) {
                                        throw new RuntimeException(e);
                                    }
                                    while (session.isPlaying()) {
                                    }
                                    // 发布会话关闭请求事件，而不是直接调用SessionManager
                                    eventPublisher.publishEvent(new SessionCloseRequestEvent(this, session.getSessionId(), "exit"));

                                }
                            })
                            .exceptionally(e -> {
                                e.printStackTrace();
                                return null;
                            });

                    return;
                }
            }

            // 初始化对话状态
            initChat(session);

            final var userMessage = new UserMessage(inputText);

            // 设置LLM生成消息的时间戳作为Assistant消息的创建时间戳，也用于约定保存音频文件的路径。一定要在LLM前设置时间戳。
            final var assistantTimeMillis = System.currentTimeMillis();
            session.setAssistantTimeMillis(assistantTimeMillis);

            // 收集所有句子的音频发送完成Future
            List<CompletableFuture<Void>> audioCompletionFutures = new ArrayList<>();

            // 使用句子切分处理流式响应
            chatService.chatSentenceStream(session, new ChatParams(inputText), false)
                    .subscribe(
                            sentence -> {
                                logger.info("处理句子: seq={}, text='{}', isFirst={}, isLast={}",
                                        sentence.getSeq(), sentence.getText(), sentence.isFirst(),
                                        sentence.isLast());

                                // 处理句子并收集Future
                                var audioFuture = handleSentence(session, sentence);
                                audioCompletionFutures.add(audioFuture);
                            },
                            error -> {
                                logger.error("流式处理出错: {}", error.getMessage(), error);
                                // 创建错误句子对象
                                var errorSentence = new Sentence(1, "抱歉，我在处理您的请求时遇到了问题。", true, true);
                                handleSentence(session, errorSentence);

                                // 即使出错也要保存用户消息到数据库
                                Thread.startVirtualThread(() -> {
                                    var errorAssistantMessage = new AssistantMessage(errorSentence.getText());
                                    session.getConversation().addMessage(userMessage, session.getUserTimeMillis(), errorAssistantMessage, session.getAssistantTimeMillis());
                                });
                            },
                            () -> {
                                // 如果是最后一句，等待所有音频发送完成后再保存对话消息到数据库
                                CompletableFuture.allOf(audioCompletionFutures.toArray(new CompletableFuture[0]))
                                        .thenRun(() -> {
                                            logger.info("所有音频发送完成 - sessionId: {}", session.getSessionId());

                                            Thread.startVirtualThread(() -> {
                                                // 完整的 LLM 回复文本
                                                var replyText = session.getAssistantResponse();

                                                if (!replyText.isEmpty()) {
                                                    var assistantMessage = new AssistantMessage(replyText);

                                                    // 保存用户消息和助手消息到数据库
                                                    session.getConversation().addMessage(userMessage, session.getUserTimeMillis(), assistantMessage, session.getAssistantTimeMillis());
                                                }
                                            });

                                            logger.info("Sentence handle completed, check task chain.....");
                                            if (session.getIsCurrTaskDone() && session.getTaskChain() != null) {
                                                sendOneSentence(session, "和你聊天真开心！现在，让我们放松一下", false)
                                                        .thenRun(() -> {
                                                            logger.info("should play story here {}", session.isPlaying());

                                                            var deviceId = session.getSysDevice().getDeviceId();

                                                            var media = mediaMapper.findLatest("story");
                                                            if (media == null) return;

                                                            while (session.isPlaying()) {
                                                                logger.info("Waiting for session stop playing");
                                                                // wait until stop playing
                                                                try {
                                                                    TimeUnit.MILLISECONDS.sleep(100);
                                                                } catch (InterruptedException e) {
                                                                    throw new RuntimeException(e);
                                                                }
                                                            }

                                                            var topic = STR."devices/p2p/GID@@@\{deviceId.replaceAll(":", "_")}";
                                                            mqttServerPublish.send(topic, "{\"type\":\"player\"}");

                                                            audioPushService.sendAudioMessage(session, media.getAssetUrl(), media.getTitle(), true, true);
                                                        });
                                            }
                                        })
                                        .exceptionally(throwable -> {
                                            logger.error("等待音频发送完成时出错 - sessionId: {}, error: {}",
                                                    session.getSessionId(), throwable.getMessage(), throwable);
                                            return null;
                                        });
                            });
        });
    }

    /**
     * 中止当前对话
     */
    public void abortDialogue(ChatSession session, String reason) {
        try {
            String sessionId = session.getSessionId();
            logger.info("中止对话 - SessionId: {}, Reason: {}", sessionId, reason);

            // 客户端主动中断
            session.setClientAbort(true);

            // 关闭音频流
            session.setAudioSinks(null);
            session.setStreamingState(false);

            if (session.isMusicPlaying()) {
                musicService.stopMusic(session);
                huiBenService.stopHuiBen(session);
                return;
            }

            // 终止语音发送
            audioService.sendStop(session);
        } catch (Exception e) {
            logger.error("中止对话失败: {}", e.getMessage(), e);
        }
    }

    public boolean sendStartSentence(ChatSession session, StartParams params) {
        var sentence = CozeUtil.genStartSentence(params);
        sendOneSentence(session, sentence, true);
        return true;
    }

    /**
     * 所有音频发送完成后的回调处理
     */
    private void onAllAudioSendingCompleted(ChatSession session) {
        logger.info("所有音频发送完成回调 - sessionId: {}", session.getSessionId());

        // 在这里可以添加所有音频发送完成后的自定义处理逻辑
        // 例如：
        // 1. 更新统计信息
        // 2. 触发后续任务
        // 3. 发送完成通知
        // 4. 清理临时资源

        // 示例：记录对话完成时间
        session.setLastActivityTime(Instant.now());
    }

    /**
     * 清理会话资源
     */
    public void cleanupSession(String sessionId) {
        llmStartTimes.remove(sessionId);

        // 清理音频发送顺序控制
        pendingSentences.remove(sessionId);
        nextPlaySeq.remove(sessionId);

        // 清理句子发送完成回调
        sentenceCompletionFutures.entrySet().removeIf(entry -> entry.getKey().startsWith(sessionId + "_"));

        // 清理AudioService中的资源
        audioService.cleanupSession(sessionId);
    }

}
